<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="<PERSON>, <PERSON>, and Bootstrap contributors">
    <meta name="generator" content="Hugo 0.88.1">
    <title>圖片分析小幫手</title>

    <link rel="canonical" href="https://getbootstrap.com/docs/5.1/examples/carousel/">

    <!-- Bootstrap core CSS -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <style>
      .bd-placeholder-img {
        font-size: 1.125rem;
        text-anchor: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
      }

      @media (min-width: 768px) {
        .bd-placeholder-img-lg {
          font-size: 3.5rem;
        }
      }
      
      #image-container {
        margin: 30px 0;
        text-align: center;
      }

      #preview-image {
        max-width: 100%;
        max-height: 300px;
      }

      #output-text {
        width: 100%;
        height: 300px;
        margin-top: 40px;
      }

      #prompt-input {
        width: 100%;
        margin-bottom: 10px;
      }

      #analyze-btn {
        display: block;
        margin: 10px auto;
      }
    </style>

    <!-- Custom styles for this template -->
    <link href="css/carousel.css" rel="stylesheet">
  </head>
  <body>
    
    <header>
      <nav class="navbar navbar-expand-md navbar-dark fixed-top bg-dark">
        <div class="container-fluid">
          <a class="navbar-brand" href="#">圖片分析小幫手</a>
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <div class="collapse navbar-collapse" id="navbarCollapse">
            <ul class="navbar-nav me-auto mb-2 mb-md-0">
              <li class="nav-item">
                <a class="nav-link active" aria-current="page" href="#">Home</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#">下載工具包</a>
              </li>
            </ul>
            <form class="d-flex">
              <input class="form-control me-2" type="search" placeholder="Search" aria-label="Search">
              <button class="btn btn-outline-success" type="submit">Search</button>
            </form>
          </div>
        </div>
      </nav>
    </header>

    <main>
      <div class="container marketing" style="margin-top: 80px;">
        <h2>圖片分析工具</h2>
        <input type="text" id="prompt-input" style="height:50px;" placeholder="輸入 prompt">
        <input type="file" id="image-input" accept="image/*" class="form-control">
        <div id="image-container">
          <img id="preview-image" src="" alt="預覽圖片" style="display: none;">
        </div>
        <textarea id="output-text" placeholder="分析結果將顯示在這裡" readonly></textarea>
        <button id="analyze-btn" class="btn btn-primary">分析圖片</button>
      </div>

      <!-- FOOTER -->
      <footer class="container">
        <p class="float-end"><a href="#">Back to top</a></p>
        <p>&copy; 2017–2021 Company, Inc. &middot; <a href="#">Privacy</a> &middot; <a href="#">Terms</a></p>
      </footer>
    </main>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script>
      const subscription_key = "ecb8f0522cc34c17871f23ec3d037ead";  // 請確保這是您的實際 Azure 訂閱金鑰
      const Azure_url = "*********************************/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-01";

      // 載入圖片檔案
      document.getElementById('image-input').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function(e) {
            document.getElementById('preview-image').src = e.target.result;
            document.getElementById('preview-image').style.display = 'block';
          }
          reader.readAsDataURL(file);
        }
      });

      // 剪貼簿貼上圖片
      document.addEventListener('paste', function(e) {
        const items = e.clipboardData.items;
        for (const item of items) {
          if (item.type.startsWith('image/')) {
            const file = item.getAsFile();
            const reader = new FileReader();
            reader.onload = function(event) {
              document.getElementById('preview-image').src = event.target.result;
              document.getElementById('preview-image').style.display = 'block';
            };
            reader.readAsDataURL(file);
          }
        }
      });

      // 分析圖片
      document.getElementById('analyze-btn').addEventListener('click', async function() {
        const imageData = document.getElementById('preview-image').src;
        const prompt = document.getElementById('prompt-input').value || "請分析這張圖片的內容";
        const outputText = document.getElementById('output-text');

        if (!imageData) {
          alert('請先上傳圖片或貼上圖片');
          return;
        }

        const headers = {
          'Content-Type': 'application/json',
          'Ocp-Apim-Subscription-Key': subscription_key,
          'api-key': subscription_key
        };

        const data = {
          messages: [
            {
              role: "system", 
              content: "你是一個專業的圖片分析助手，請詳細分析提供的圖片。"
            },
            {
              role: "user", 
              content: [
                {
                  type: "text",
                  text: prompt
                },
                {
                  type: "image_url",
                  image_url: {
                    url: imageData
                  }
                }
              ]
            }
          ],
          max_tokens: 2000,
          temperature: 0.7
        };

        try {
          outputText.value = "正在分析圖片，請稍候...";
          
          const response = await fetch(Azure_url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(data)
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();
          
          if (result.choices && result.choices.length > 0) {
            outputText.value = result.choices[0].message.content.trim();
          } else {
            throw new Error('API response format is unexpected');
          }
        } catch (error) {
          console.error('Error:', error);
          outputText.value = `分析過程發生錯誤：${error.message}\n\n請確認：\n1. API金鑰是否正確\n2. API端點是否正確\n3. 圖片格式是否支援\n4. 網路連線是否正常`;
        }
      });
    </script>
  </body>
</html>
