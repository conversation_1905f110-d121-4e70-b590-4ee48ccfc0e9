<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>圖片分析API測試頁面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="date"], input[type="file"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        #preview-container {
            margin: 20px 0;
            text-align: center;
        }
        #preview-image {
            max-width: 100%;
            max-height: 300px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        #result-container {
            margin-top: 30px;
        }
        #result-text {
            width: 100%;
            height: 400px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 14px;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .api-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #e9ecef;
            border: none;
            cursor: pointer;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #007bff;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>圖片分析API測試頁面</h1>
        
        <div class="api-info">
            <strong>API端點：</strong> http://localhost:8000<br>
            <strong>支援格式：</strong> 檔案上傳 / Base64編碼<br>
            <strong>輸出格式：</strong> 結構化JSON資料
        </div>

        <div class="tabs">
            <button class="tab active" onclick="switchTab('file')">檔案上傳測試</button>
            <button class="tab" onclick="switchTab('base64')">Base64測試</button>
        </div>

        <!-- 檔案上傳測試 -->
        <div id="file-tab" class="tab-content active">
            <form id="file-form">
                <div class="form-group">
                    <label for="file-input">選擇圖片檔案：</label>
                    <input type="file" id="file-input" accept="image/*" required>
                </div>
                
                <div class="form-group">
                    <label for="prompt-input">分析提示詞：</label>
                    <textarea id="prompt-input" placeholder="請分析這張圖片的內容">請分析此檢驗流程圖片</textarea>
                </div>
                
                <div class="form-group">
                    <label for="date-input">日期參數：</label>
                    <input type="date" id="date-input">
                </div>
                
                <div class="form-group">
                    <label for="text-input">文字參數：</label>
                    <input type="text" id="text-input" placeholder="附加文字資訊">
                </div>
                
                <button type="submit" class="btn" id="analyze-file-btn">分析圖片</button>
                <button type="button" class="btn" onclick="clearResults()">清除結果</button>
            </form>
        </div>

        <!-- Base64測試 -->
        <div id="base64-tab" class="tab-content">
            <form id="base64-form">
                <div class="form-group">
                    <label for="base64-input">Base64圖片資料：</label>
                    <textarea id="base64-input" placeholder="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA... 或直接貼上圖片 (Ctrl+V)" style="height: 120px;"></textarea>
                    <small style="color: #666;">提示：可以直接在此區域貼上圖片 (Ctrl+V)，系統會自動轉換為Base64格式</small>
                </div>
                
                <div class="form-group">
                    <label for="prompt-input-b64">分析提示詞：</label>
                    <textarea id="prompt-input-b64" placeholder="請分析這張圖片的內容">請分析此檢驗流程圖片</textarea>
                </div>
                
                <div class="form-group">
                    <label for="date-input-b64">日期參數：</label>
                    <input type="date" id="date-input-b64">
                </div>
                
                <div class="form-group">
                    <label for="text-input-b64">文字參數：</label>
                    <input type="text" id="text-input-b64" placeholder="附加文字資訊">
                </div>
                
                <button type="submit" class="btn" id="analyze-base64-btn">分析圖片</button>
                <button type="button" class="btn" onclick="clearResults()">清除結果</button>
            </form>
        </div>

        <!-- 圖片預覽 -->
        <div id="preview-container" style="display: none;">
            <h3>圖片預覽：</h3>
            <img id="preview-image" alt="預覽圖片">
        </div>

        <!-- 結果顯示 -->
        <div id="result-container">
            <h3>分析結果：</h3>
            <textarea id="result-text" readonly placeholder="分析結果將顯示在這裡..."></textarea>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';

        // 切換標籤
        function switchTab(tabName) {
            // 隱藏所有標籤內容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 顯示選中的標籤
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }

        // 檔案上傳預覽
        document.getElementById('file-input').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('preview-image').src = e.target.result;
                    document.getElementById('preview-container').style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });

        // 檔案上傳表單提交
        document.getElementById('file-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('file-input');
            const promptInput = document.getElementById('prompt-input');
            const dateInput = document.getElementById('date-input');
            const textInput = document.getElementById('text-input');
            const resultText = document.getElementById('result-text');
            const analyzeBtn = document.getElementById('analyze-file-btn');

            if (!fileInput.files[0]) {
                alert('請選擇圖片檔案');
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('prompt', promptInput.value);
            if (dateInput.value) formData.append('date', dateInput.value);
            if (textInput.value) formData.append('text', textInput.value);

            try {
                analyzeBtn.disabled = true;
                analyzeBtn.textContent = '分析中...';
                resultText.value = '正在分析圖片，請稍候...';

                const response = await fetch(`${API_BASE_URL}/analyze-image-file`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    try {
                        // 嘗試解析JSON並格式化
                        const parsedResult = JSON.parse(result.result);
                        resultText.value = JSON.stringify(parsedResult, null, 2);
                    } catch (jsonError) {
                        // 如果JSON解析失敗，直接顯示原始結果
                        console.warn('JSON解析失敗，顯示原始結果:', jsonError);
                        resultText.value = result.result;
                    }
                    showMessage('分析完成！', 'success');
                } else {
                    resultText.value = `錯誤：${result.error}`;
                    showMessage('分析失敗', 'error');
                }
            } catch (error) {
                resultText.value = `網路錯誤：${error.message}`;
                showMessage('網路連線錯誤', 'error');
            } finally {
                analyzeBtn.disabled = false;
                analyzeBtn.textContent = '分析圖片';
            }
        });

        // Base64表單提交
        document.getElementById('base64-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const base64Input = document.getElementById('base64-input');
            const promptInput = document.getElementById('prompt-input-b64');
            const dateInput = document.getElementById('date-input-b64');
            const textInput = document.getElementById('text-input-b64');
            const resultText = document.getElementById('result-text');
            const analyzeBtn = document.getElementById('analyze-base64-btn');

            if (!base64Input.value.trim()) {
                alert('請輸入Base64圖片資料');
                return;
            }

            const requestData = {
                image_base64: base64Input.value.trim(),
                prompt: promptInput.value,
                date: dateInput.value || null,
                text: textInput.value || null
            };

            try {
                analyzeBtn.disabled = true;
                analyzeBtn.textContent = '分析中...';
                resultText.value = '正在分析圖片，請稍候...';

                const response = await fetch(`${API_BASE_URL}/analyze-image-base64`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();
                
                if (result.success) {
                    try {
                        // 嘗試解析JSON並格式化
                        const parsedResult = JSON.parse(result.result);
                        resultText.value = JSON.stringify(parsedResult, null, 2);
                    } catch (jsonError) {
                        // 如果JSON解析失敗，直接顯示原始結果
                        console.warn('JSON解析失敗，顯示原始結果:', jsonError);
                        resultText.value = result.result;
                    }
                    showMessage('分析完成！', 'success');
                } else {
                    resultText.value = `錯誤：${result.error}`;
                    showMessage('分析失敗', 'error');
                }
            } catch (error) {
                resultText.value = `網路錯誤：${error.message}`;
                showMessage('網路連線錯誤', 'error');
            } finally {
                analyzeBtn.disabled = false;
                analyzeBtn.textContent = '分析圖片';
            }
        });

        // 清除結果
        function clearResults() {
            document.getElementById('result-text').value = '';
            document.getElementById('preview-container').style.display = 'none';
            document.querySelectorAll('.message').forEach(msg => msg.remove());
        }

        // 顯示訊息
        function showMessage(text, type) {
            const existingMessages = document.querySelectorAll('.message');
            existingMessages.forEach(msg => msg.remove());

            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.textContent = text;
            document.querySelector('.container').insertBefore(message, document.getElementById('result-container'));

            setTimeout(() => {
                message.remove();
            }, 3000);
        }

        // 設定今天日期為預設值
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('date-input').value = today;
        document.getElementById('date-input-b64').value = today;

        // Base64區域貼上圖片功能
        document.getElementById('base64-input').addEventListener('paste', function(e) {
            e.preventDefault();
            
            const items = e.clipboardData.items;
            for (const item of items) {
                if (item.type.startsWith('image/')) {
                    const file = item.getAsFile();
                    const reader = new FileReader();
                    reader.onload = function(event) {
                        document.getElementById('base64-input').value = event.target.result;
                        // 同時更新預覽圖片
                        document.getElementById('preview-image').src = event.target.result;
                        document.getElementById('preview-container').style.display = 'block';
                        showMessage('圖片已貼上並轉換為Base64格式', 'success');
                    };
                    reader.readAsDataURL(file);
                    return;
                }
            }
            
            // 如果沒有圖片，檢查是否為文字內容
            const text = e.clipboardData.getData('text');
            if (text) {
                document.getElementById('base64-input').value = text;
            }
        });

        // 為Base64輸入框添加拖放功能
        const base64Input = document.getElementById('base64-input');
        
        base64Input.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.style.backgroundColor = '#e3f2fd';
        });

        base64Input.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.style.backgroundColor = '';
        });

        base64Input.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.style.backgroundColor = '';
            
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    document.getElementById('base64-input').value = event.target.result;
                    // 同時更新預覽圖片
                    document.getElementById('preview-image').src = event.target.result;
                    document.getElementById('preview-container').style.display = 'block';
                    showMessage('圖片已拖放並轉換為Base64格式', 'success');
                };
                reader.readAsDataURL(files[0]);
            }
        });

        // Base64輸入框內容變化時更新預覽
        document.getElementById('base64-input').addEventListener('input', function(e) {
            const base64Data = e.target.value.trim();
            if (base64Data.startsWith('data:image/')) {
                document.getElementById('preview-image').src = base64Data;
                document.getElementById('preview-container').style.display = 'block';
            }
        });
    </script>
</body>
</html>


