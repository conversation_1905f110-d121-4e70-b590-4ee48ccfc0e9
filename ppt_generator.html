<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI簡報生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }

        input[type="text"], textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .download-btn {
            background: #28a745;
            margin-top: 10px;
            display: inline-block;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 AI簡報生成器</h1>
        
        <form id="ppt-form">
            <div class="form-group">
                <label for="topic">簡報主題 *</label>
                <input type="text" id="topic" name="topic" placeholder="例如：人工智慧在醫療領域的應用" required>
            </div>

            <div class="form-group">
                <label for="slides">投影片數量</label>
                <select id="slides" name="slides">
                    <option value="5">5張投影片</option>
                    <option value="8" selected>8張投影片</option>
                    <option value="10">10張投影片</option>
                    <option value="12">12張投影片</option>
                    <option value="15">15張投影片</option>
                </select>
            </div>

            <div class="form-group">
                <label for="style">簡報風格</label>
                <select id="style" name="style">
                    <option value="professional">專業商務</option>
                    <option value="creative">創意設計</option>
                    <option value="academic">學術研究</option>
                    <option value="simple">簡約風格</option>
                </select>
            </div>

            <div class="form-group">
                <label for="additional">額外要求（選填）</label>
                <textarea id="additional" name="additional" placeholder="例如：請加入圖表、案例研究、包含結論建議等..."></textarea>
            </div>

            <button type="submit" class="btn" id="generate-btn">
                🚀 生成簡報
            </button>
            
            <button type="button" class="btn" id="test-btn" style="background: #6c757d; margin-top: 10px;">
                🔧 測試API連接
            </button>
        </form>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在生成簡報，請稍候...</p>
            <p><small>這可能需要1-2分鐘時間</small></p>
        </div>

        <div class="result" id="result"></div>
    </div>

    <script>
        const API_URL = 'https://ppt-generator-api.azurewebsites.net/convert_to_pptx';
        
        document.getElementById('ppt-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const topic = document.getElementById('topic').value.trim();
            const slides = document.getElementById('slides').value;
            const style = document.getElementById('style').value;
            const additional = document.getElementById('additional').value.trim();
            
            if (!topic) {
                showResult('請輸入簡報主題', 'error');
                return;
            }

            // 構建提示詞
            let prompt = `請為我創建一個關於「${topic}」的簡報，包含${slides}張投影片。`;
            
            switch(style) {
                case 'professional':
                    prompt += '風格要求：專業商務風格，適合企業會議使用。';
                    break;
                case 'creative':
                    prompt += '風格要求：創意設計風格，視覺效果豐富。';
                    break;
                case 'academic':
                    prompt += '風格要求：學術研究風格，內容嚴謹詳實。';
                    break;
                case 'simple':
                    prompt += '風格要求：簡約風格，重點突出。';
                    break;
            }
            
            if (additional) {
                prompt += ` 額外要求：${additional}`;
            }
            
            prompt += ' 請確保內容結構完整，包含標題頁、目錄、主要內容和結論。';

            try {
                showLoading(true);
                hideResult();
                
                console.log('發送請求到:', API_URL);
                console.log('請求內容:', { prompt: prompt });
                
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: prompt
                    })
                });

                console.log('回應狀態:', response.status);
                console.log('回應標頭:', [...response.headers.entries()]);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('API錯誤回應:', errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                console.log('API回應:', result);
                
                if (result.url) {
                    showResult(`
                        <strong>✅ 簡報生成成功！</strong><br>
                        主題：${topic}<br>
                        投影片數量：${slides}張<br>
                        <a href="${result.url}" class="download-btn" target="_blank">📥 下載簡報</a>
                    `, 'success');
                } else if (result.download_url) {
                    showResult(`
                        <strong>✅ 簡報生成成功！</strong><br>
                        主題：${topic}<br>
                        投影片數量：${slides}張<br>
                        <a href="${result.download_url}" class="download-btn" target="_blank">📥 下載簡報</a>
                    `, 'success');
                } else if (result.error) {
                    throw new Error(`API錯誤: ${result.error}`);
                } else {
                    throw new Error('API回應格式錯誤，未找到下載連結');
                }

            } catch (error) {
                console.error('詳細錯誤:', error);
                
                let errorMessage = '';
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    errorMessage = `❌ 網路連線錯誤<br><br>
                        <strong>CORS問題解決方案：</strong><br>
                        1. 使用本地HTTP服務器：<br>
                        &nbsp;&nbsp;<code>python -m http.server 8000</code><br>
                        &nbsp;&nbsp;然後訪問 <code>http://localhost:8000/ppt_generator.html</code><br><br>
                        2. 或使用VS Code Live Server擴展<br><br>
                        3. 或部署到GitHub Pages等託管服務`;
                } else if (error.message.includes('HTTP')) {
                    errorMessage = `❌ API服務錯誤<br><br>${error.message}<br><br>請稍後再試或聯繫技術支援`;
                } else {
                    errorMessage = `❌ 生成失敗<br><br>錯誤詳情：${error.message}`;
                }
                
                showResult(errorMessage, 'error');
            } finally {
                showLoading(false);
            }
        });

        function showLoading(show) {
            const loading = document.getElementById('loading');
            const btn = document.getElementById('generate-btn');
            
            if (show) {
                loading.style.display = 'block';
                btn.disabled = true;
                btn.textContent = '生成中...';
            } else {
                loading.style.display = 'none';
                btn.disabled = false;
                btn.textContent = '🚀 生成簡報';
            }
        }

        function showResult(message, type) {
            const result = document.getElementById('result');
            result.innerHTML = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }

        function hideResult() {
            const result = document.getElementById('result');
            result.style.display = 'none';
        }

        // 測試API連接
        document.getElementById('test-btn').addEventListener('click', async function() {
            const btn = this;
            const originalText = btn.textContent;
            
            try {
                btn.disabled = true;
                btn.textContent = '測試中...';
                
                // 先測試API文檔頁面是否可訪問
                const docsResponse = await fetch('https://ppt-generator-api.azurewebsites.net/docs', {
                    method: 'GET',
                    mode: 'no-cors'
                });
                
                showResult('✅ API服務可訪問，但需要解決CORS問題<br><br><strong>請使用以下方法之一：</strong><br>1. 本地HTTP服務器：<code>python -m http.server 8000</code><br>2. VS Code Live Server擴展<br>3. 部署到網站託管服務', 'success');
                
            } catch (error) {
                console.error('API測試錯誤:', error);
                showResult(`❌ 無法連接到API服務<br>錯誤: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = originalText;
            }
        });

        // 示例主題建議
        const exampleTopics = [
            '人工智慧在醫療領域的應用',
            '數位轉型策略與實施',
            '永續發展與企業社會責任',
            '遠距工作的挑戰與機會',
            '區塊鏈技術的商業應用',
            '數據分析在決策中的重要性'
        ];

        // 隨機顯示示例主題
        document.getElementById('topic').addEventListener('focus', function() {
            if (!this.value) {
                const randomTopic = exampleTopics[Math.floor(Math.random() * exampleTopics.length)];
                this.placeholder = `例如：${randomTopic}`;
            }
        });
    </script>
</body>
</html>






