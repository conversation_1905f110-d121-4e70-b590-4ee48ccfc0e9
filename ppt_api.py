from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel
import httpx
import json
import os
import tempfile
import uuid
from datetime import datetime
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor

app = FastAPI(title="PowerPoint Generator API", description="Generate PowerPoint presentations from markdown content")

# 允許跨域請求
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Azure OpenAI 設定
SUBSCRIPTION_KEY = "ecb8f0522cc34c17871f23ec3d037ead"
AZURE_URL = "*********************************/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-01"

class MarkdownInput(BaseModel):
    markdown_content: str

class PPTGenerationRequest(BaseModel):
    prompt: str

# 創建輸出目錄
OUTPUT_DIR = "generated_ppts"
os.makedirs(OUTPUT_DIR, exist_ok=True)

async def generate_markdown_with_llm(prompt: str) -> str:
    """使用Azure OpenAI生成Markdown格式的簡報內容"""
    
    system_prompt = """你是一個專業的簡報內容生成專家。請根據用戶的要求生成結構化的Markdown格式簡報內容。

請遵循以下格式：
# 簡報標題

## 投影片1: 標題頁
- **標題**: [主標題]
- **副標題**: [副標題]
- **作者**: [作者信息]

## 投影片2: 目錄
- [要點1]
- [要點2]
- [要點3]

## 投影片3: [章節標題]
- **重點1**: [內容]
- **重點2**: [內容]
- **重點3**: [內容]

繼續按照這個格式生成所有投影片內容。確保：
1. 每個投影片都有清晰的標題
2. 內容簡潔明瞭，適合投影片展示
3. 使用項目符號和粗體來突出重點
4. 包含結論或總結投影片"""

    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                AZURE_URL,
                headers={
                    "Ocp-Apim-Subscription-Key": SUBSCRIPTION_KEY,
                    "Content-Type": "application/json"
                },
                json={
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    "max_tokens": 3000,
                    "temperature": 0.7
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                raise HTTPException(status_code=500, detail=f"Azure OpenAI API錯誤: {response.status_code}")
                
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成內容時發生錯誤: {str(e)}")

def parse_markdown_to_slides(markdown_content: str) -> list:
    """解析Markdown內容為投影片結構"""
    slides = []
    current_slide = None
    
    lines = markdown_content.split('\n')
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 檢測投影片標題 (## 開頭)
        if line.startswith('## '):
            if current_slide:
                slides.append(current_slide)
            current_slide = {
                'title': line[3:].strip(),
                'content': []
            }
        # 檢測內容項目
        elif line.startswith('- ') and current_slide:
            content = line[2:].strip()
            current_slide['content'].append(content)
        # 檢測主標題 (# 開頭)
        elif line.startswith('# ') and not current_slide:
            current_slide = {
                'title': line[2:].strip(),
                'content': [],
                'is_title_slide': True
            }
    
    # 添加最後一個投影片
    if current_slide:
        slides.append(current_slide)
    
    return slides

def create_powerpoint(slides_data: list, filename: str) -> str:
    """創建PowerPoint文件"""
    prs = Presentation()
    
    for slide_data in slides_data:
        # 選擇版面配置
        if slide_data.get('is_title_slide'):
            slide_layout = prs.slide_layouts[0]  # 標題投影片
        else:
            slide_layout = prs.slide_layouts[1]  # 標題和內容
        
        slide = prs.slides.add_slide(slide_layout)
        
        # 設置標題
        title = slide.shapes.title
        title.text = slide_data['title']
        
        # 設置內容
        if not slide_data.get('is_title_slide') and slide_data['content']:
            content_placeholder = slide.placeholders[1]
            text_frame = content_placeholder.text_frame
            text_frame.clear()
            
            for i, content_item in enumerate(slide_data['content']):
                if i == 0:
                    p = text_frame.paragraphs[0]
                else:
                    p = text_frame.add_paragraph()
                
                # 處理粗體文字
                if '**' in content_item:
                    parts = content_item.split('**')
                    for j, part in enumerate(parts):
                        run = p.add_run()
                        run.text = part
                        if j % 2 == 1:  # 奇數索引是粗體部分
                            run.font.bold = True
                else:
                    run = p.add_run()
                    run.text = content_item
                
                p.level = 0
    
    # 保存文件
    filepath = os.path.join(OUTPUT_DIR, filename)
    prs.save(filepath)
    return filepath

@app.post("/convert-to-pptx/")
async def convert_to_pptx(request: MarkdownInput):
    """將Markdown內容轉換為PowerPoint文件"""
    try:
        # 解析Markdown為投影片結構
        slides_data = parse_markdown_to_slides(request.markdown_content)
        
        if not slides_data:
            raise HTTPException(status_code=400, detail="無法解析Markdown內容")
        
        # 生成唯一文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"presentation_{timestamp}_{uuid.uuid4().hex[:8]}.pptx"
        
        # 創建PowerPoint文件
        filepath = create_powerpoint(slides_data, filename)
        
        # 返回下載連結
        download_url = f"/download/{filename}"
        
        return {
            "success": True,
            "message": "簡報生成成功",
            "download_url": download_url,
            "filename": filename
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成簡報時發生錯誤: {str(e)}")

@app.post("/generate-ppt/")
async def generate_ppt(request: PPTGenerationRequest):
    """根據提示詞生成PowerPoint簡報"""
    try:
        # 使用LLM生成Markdown內容
        markdown_content = await generate_markdown_with_llm(request.prompt)
        
        # 轉換為PowerPoint
        markdown_request = MarkdownInput(markdown_content=markdown_content)
        result = await convert_to_pptx(markdown_request)
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成簡報時發生錯誤: {str(e)}")

@app.get("/download/{filename}")
async def download_file(filename: str):
    """下載生成的PowerPoint文件"""
    filepath = os.path.join(OUTPUT_DIR, filename)
    
    if not os.path.exists(filepath):
        raise HTTPException(status_code=404, detail="文件不存在")
    
    return FileResponse(
        path=filepath,
        filename=filename,
        media_type="application/vnd.openxmlformats-officedocument.presentationml.presentation"
    )

@app.get("/")
async def root():
    return {"message": "PowerPoint Generator API", "version": "1.0.0"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)
