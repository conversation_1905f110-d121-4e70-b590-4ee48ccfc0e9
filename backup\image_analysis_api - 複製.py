from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Union
import httpx
import base64
import io
from PIL import Image
import json

app = FastAPI(title="圖片分析API", description="接收圖片並使用Azure OpenAI進行分析")

# 允許跨域請求
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Azure OpenAI 設定
SUBSCRIPTION_KEY = "ecb8f0522cc34c17871f23ec3d037ead"
AZURE_URL = "*********************************/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-01"

class ImageAnalysisRequest(BaseModel):
    prompt: Optional[str] = "請分析這張圖片的內容"
    image_base64: Optional[str] = None

class ImageAnalysisResponse(BaseModel):
    result: str
    success: bool
    error: Optional[str] = None

def image_to_base64(image_file) -> str:
    """將圖片檔案轉換為base64編碼"""
    image = Image.open(image_file)
    buffer = io.BytesIO()
    image.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()
    return f"data:image/png;base64,{img_str}"

async def analyze_image_with_llm(image_data: str, prompt: str, date: str = None, text: str = None) -> str:
    """使用Azure OpenAI分析圖片"""
    try:
        headers = {
            'Content-Type': 'application/json',
            'Ocp-Apim-Subscription-Key': SUBSCRIPTION_KEY,
            'api-key': SUBSCRIPTION_KEY
        }
        
        # 構建標準化分析提示詞
        standardized_prompt = """
請分析此檢驗流程圖片，以JSON格式輸出以下資訊：
{
  "company": "公司",
  "no_item": "表單編號",
  "flow_number": "FlowER",
  "kind_code": "表單類型",
  "status": "表單狀態",
  "pay_to_company": "付款對象",
  "test_item": "表單摘要",
  "stages": [
    {
      "stage_name": "階段名稱",
      "status": "完成/進行中/未開始",
      "date": "日期(如有)"
    }
  ],
  "expected_completion": "預計完成時間",
  "last_update": "最後更新時間"
}

只輸出JSON格式，不要其他說明文字。
"""
        
        if date:
            standardized_prompt += f"\n參考日期：{date}"
        if text:
            standardized_prompt += f"\n附加資訊：{text}"
        
        data = {
            "messages": [
                {
                    "role": "system", 
                    "content": "你是專業的檢驗流程分析助手，專門分析檢驗進度圖片並輸出結構化JSON資料。"
                },
                {
                    "role": "user", 
                    "content": [
                        {"type": "text", "text": standardized_prompt},
                        {"type": "image_url", "image_url": {"url": image_data}}
                    ]
                }
            ],
            "max_tokens": 2000,
            "temperature": 0.1
        }
        
        print(f"發送請求到 Azure OpenAI...")
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(AZURE_URL, headers=headers, json=data)
            
            print(f"Azure API 回應狀態碼: {response.status_code}")
            
            if response.status_code != 200:
                error_text = response.text
                print(f"Azure API 錯誤回應: {error_text}")
                raise HTTPException(status_code=response.status_code, 
                                  detail=f"Azure API error: {error_text}")
            
            result = response.json()
            raw_content = result['choices'][0]['message']['content'].strip()
            
            # 清理markdown格式的代碼塊
            cleaned_content = raw_content
            if cleaned_content.startswith('```json'):
                cleaned_content = cleaned_content[7:]  # 移除 ```json
            if cleaned_content.startswith('```'):
                cleaned_content = cleaned_content[3:]   # 移除 ```
            if cleaned_content.endswith('```'):
                cleaned_content = cleaned_content[:-3]  # 移除結尾的 ```
            
            cleaned_content = cleaned_content.strip()
            print(f"清理後的內容: {cleaned_content[:100]}...")
            
            print(f"Azure API 回應成功")
            return cleaned_content
            
    except httpx.TimeoutException as e:
        print(f"請求超時錯誤: {str(e)}")
        raise Exception(f"請求超時: {str(e)}")
    except httpx.RequestError as e:
        print(f"網路請求錯誤: {str(e)}")
        raise Exception(f"網路連線錯誤: {str(e)}")
    except KeyError as e:
        print(f"API回應格式錯誤: {str(e)}")
        raise Exception(f"API回應格式異常: {str(e)}")
    except Exception as e:
        print(f"analyze_image_with_llm 發生未知錯誤: {str(e)}")
        raise Exception(f"圖片分析失敗: {str(e)}")

@app.post("/analyze-image-file", response_model=ImageAnalysisResponse)
async def analyze_image_file(
    file: UploadFile = File(...),
    prompt: str = Form("請分析這張圖片的內容"),
    date: str = Form(None),
    text: str = Form(None)
):
    """接收圖片檔案進行分析"""
    try:
        # 檢查檔案類型
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="請上傳圖片檔案")
        
        # 轉換為base64
        image_data = image_to_base64(file.file)
        
        # 分析圖片
        result = await analyze_image_with_llm(image_data, prompt, date, text)
        
        return ImageAnalysisResponse(result=result, success=True)
        
    except Exception as e:
        print(f"API錯誤詳情: {str(e)}")  # 在服務端印出詳細錯誤
        return ImageAnalysisResponse(
            result="", 
            success=False, 
            error=f"分析過程發生錯誤：{str(e)}"
        )

@app.post("/analyze-image-base64", response_model=ImageAnalysisResponse)
async def analyze_image_base64(request: ImageAnalysisRequest):
    """接收base64編碼圖片進行分析"""
    try:
        print(f"收到 base64 分析請求")
        
        if not request.image_base64:
            raise HTTPException(status_code=400, detail="請提供base64編碼的圖片")
        
        print(f"圖片資料長度: {len(request.image_base64)}")
        print(f"提示詞: {request.prompt}")
        
        # 分析圖片
        result = await analyze_image_with_llm(request.image_base64, request.prompt)
        
        print(f"分析完成，結果長度: {len(result)}")
        return ImageAnalysisResponse(result=result, success=True)
        
    except Exception as e:
        print(f"analyze_image_base64 API錯誤詳情: {str(e)}")
        print(f"錯誤類型: {type(e).__name__}")
        return ImageAnalysisResponse(
            result="", 
            success=False, 
            error=f"分析過程發生錯誤：{str(e)}"
        )

@app.get("/")
async def root():
    return {"message": "圖片分析API服務運行中"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)




