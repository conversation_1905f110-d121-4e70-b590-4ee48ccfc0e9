# PowerPoint Generator API

這是一個本地的PowerPoint生成器API，可以根據用戶輸入的主題和要求自動生成專業的PowerPoint簡報。

## 功能特色

- 🎯 根據主題自動生成簡報內容
- 🎨 支持多種簡報風格（專業商務、創意設計、學術研究、簡約風格）
- 📊 可自定義投影片數量（5-15張）
- 🤖 使用Azure OpenAI GPT-4o生成高質量內容
- 📥 直接下載生成的PPTX文件

## 安裝和運行

### 方法1：使用批次檔（推薦）
1. 雙擊 `start_api.bat` 文件
2. 等待依賴安裝完成和API啟動
3. 在瀏覽器中打開 `ppt_generator.html`

### 方法2：手動運行
1. 安裝依賴：
   ```bash
   pip install -r requirements.txt
   ```

2. 啟動API服務：
   ```bash
   python ppt_api.py
   ```

3. 在瀏覽器中打開 `ppt_generator.html`

## 使用說明

1. 在網頁界面中輸入簡報主題
2. 選擇投影片數量和風格
3. 可選擇性添加額外要求
4. 點擊「生成簡報」按鈕
5. 等待生成完成後下載PPTX文件

## API端點

- `GET /` - API根路徑，返回服務信息
- `POST /generate-ppt/` - 根據提示詞生成PowerPoint
- `POST /convert-to-pptx/` - 將Markdown內容轉換為PowerPoint
- `GET /download/{filename}` - 下載生成的文件

## 技術架構

- **後端**: FastAPI + Python
- **前端**: HTML + JavaScript
- **AI模型**: Azure OpenAI GPT-4o
- **PowerPoint生成**: python-pptx

## 注意事項

- 確保有穩定的網路連接以訪問Azure OpenAI API
- 生成的文件會保存在 `generated_ppts` 目錄中
- 建議使用現代瀏覽器以獲得最佳體驗

## 故障排除

如果遇到問題，請檢查：
1. Python環境是否正確安裝
2. 所有依賴是否已安裝
3. API服務是否正常運行在 http://localhost:8000
4. 網路連接是否正常
